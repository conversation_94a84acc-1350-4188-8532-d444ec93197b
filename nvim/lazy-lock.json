{"augment.vim": {"branch": "main", "commit": "97418c9dfc1918fa9bdd23863ea3d2e49130727f"}, "auto-dark-mode.nvim": {"branch": "master", "commit": "c31de126963ffe9403901b4b0990dde0e6999cc6"}, "auto-session": {"branch": "main", "commit": "fffb13dcbe8731b8650e5bf1caa749a485d20556"}, "avante.nvim": {"branch": "main", "commit": "fe4f7d836be08249f4522e872ec794befa763eff"}, "cmp-buffer": {"branch": "main", "commit": "b74fab3656eea9de20a9b8116afa3cfc4ec09657"}, "cmp-nvim-lsp": {"branch": "main", "commit": "a8912b88ce488f411177fc8aed358b04dc246d7b"}, "cmp-path": {"branch": "main", "commit": "c6635aae33a50d6010bf1aa756ac2398a2d54c32"}, "colorbuddy.nvim": {"branch": "master", "commit": "8b968581e5c19d22a861d5f3fe5dbd83394fa681"}, "conform.nvim": {"branch": "master", "commit": "8132ec733eed3bf415b97b76797ca41b59f51d7d"}, "copilot.lua": {"branch": "master", "commit": "c1bb86abbed1a52a11ab3944ef00c8410520543d"}, "copilot.vim": {"branch": "release", "commit": "3955014c503b0cd7b30bc56c86c56c0736ca0951"}, "dressing.nvim": {"branch": "master", "commit": "2d7c2db2507fa3c4956142ee607431ddb2828639"}, "fzf-lua": {"branch": "main", "commit": "05eaee319fcf2f5a0d378c2848641858c8b8962e"}, "gitsigns.nvim": {"branch": "main", "commit": "1b0350ab707713b2bc6c236151f1a324175347b1"}, "gruvbox.nvim": {"branch": "main", "commit": "00e38a379bab3389e187b3953566d67d494dfddd"}, "img-clip.nvim": {"branch": "main", "commit": "d8b6b030672f9f551a0e3526347699985a779d93"}, "lazy.nvim": {"branch": "main", "commit": "6c3bda4aca61a13a9c63f1c1d1b16b9d3be90d7a"}, "lazygit.nvim": {"branch": "main", "commit": "4839ab642962cc76bb1bf278427dc4c59be15072"}, "lualine.nvim": {"branch": "master", "commit": "a94fc68960665e54408fe37dcf573193c4ce82c9"}, "mason-lspconfig.nvim": {"branch": "main", "commit": "1a31f824b9cd5bc6f342fc29e9a53b60d74af245"}, "mason-tool-installer.nvim": {"branch": "main", "commit": "93a9ff9b34c91c0cb0f7de8d5f7e4abce51d8903"}, "mason.nvim": {"branch": "main", "commit": "fc98833b6da5de5a9c5b1446ac541577059555be"}, "mini.files": {"branch": "main", "commit": "90f11bfd3ecbed55c28cc836c3c62c19ecdf4de0"}, "mini.map": {"branch": "main", "commit": "6219866e6ce327365c67c016b10032f6b67e16dd"}, "mini.nvim": {"branch": "main", "commit": "f4d3cb78c7c969fa61aa93cd1c1d1bfe792616c1"}, "mini.pick": {"branch": "main", "commit": "bfe86d7a7a78f9e50bbb418358c5ab30e804804f"}, "mini.splitjoin": {"branch": "main", "commit": "ebde0e2a33ec983ade3b2bb3c495b3751b458e4c"}, "mini.surround": {"branch": "main", "commit": "1a2b59c77a0c4713a5bd8972da322f842f4821b1"}, "mini.trailspace": {"branch": "main", "commit": "13e3fcdac308ae2072a015456be88e1ecb7836d3"}, "nightfox.nvim": {"branch": "main", "commit": "ba47d4b4c5ec308718641ba7402c143836f35aa9"}, "nui.nvim": {"branch": "main", "commit": "de740991c12411b663994b2860f1a4fd0937c130"}, "nvim-autopairs": {"branch": "master", "commit": "2647cce4cb64fb35c212146663384e05ae126bdf"}, "nvim-cmp": {"branch": "main", "commit": "b5311ab3ed9c846b585c0c15b7559be131ec4be9"}, "nvim-colorizer.lua": {"branch": "master", "commit": "517df88cf2afb36652830df2c655df2da416a0ae"}, "nvim-lint": {"branch": "master", "commit": "2b0039b8be9583704591a13129c600891ac2c596"}, "nvim-lspconfig": {"branch": "master", "commit": "0112e1f77983141e1453bd37d124302f1c876c46"}, "nvim-noirbuddy": {"branch": "master", "commit": "4b690fb0c4e44683e04af789911d7f17234ba2e4"}, "nvim-treesitter": {"branch": "master", "commit": "42fc28ba918343ebfd5565147a42a26580579482"}, "nvim-ts-autotag": {"branch": "main", "commit": "a1d526af391f6aebb25a8795cbc05351ed3620b5"}, "nvim-web-devicons": {"branch": "master", "commit": "19d6211c78169e78bab372b585b6fb17ad974e82"}, "oil.nvim": {"branch": "master", "commit": "08c2bce8b00fd780fb7999dbffdf7cd174e896fb"}, "plenary.nvim": {"branch": "master", "commit": "857c5ac632080dba10aae49dba902ce3abf91b35"}, "render-markdown.nvim": {"branch": "main", "commit": "ba50c2fa9178a9b7e35c9410fb7c952bdf6de50e"}, "smear-cursor.nvim": {"branch": "main", "commit": "07ada7a52bf3c1580c8a7c0f192bf0c8ca66bd30"}, "tailwindcss-colorizer-cmp.nvim": {"branch": "main", "commit": "3d3cd95e4a4135c250faf83dd5ed61b8e5502b86"}, "telescope-fzf-native.nvim": {"branch": "main", "commit": "1f08ed60cafc8f6168b72b80be2b2ea149813e55"}, "telescope-themes": {"branch": "main", "commit": "65721365bd7a04a6c9679e76b6387b60320fd5f3"}, "telescope.nvim": {"branch": "master", "commit": "b4da76be54691e854d3e0e02c36b0245f945c2c7"}, "tokyonight.nvim": {"branch": "main", "commit": "057ef5d260c1931f1dffd0f052c685dcd14100a3"}, "ts-comments.nvim": {"branch": "main", "commit": "1bd9d0ba1d8b336c3db50692ffd0955fe1bb9f0c"}, "undotree": {"branch": "master", "commit": "b951b87b46c34356d44aa71886aecf9dd7f5788a"}, "vim-fugitive": {"branch": "master", "commit": "593f831d6f6d779cbabb70a4d1e6b1b1936a88af"}, "which-key.nvim": {"branch": "main", "commit": "370ec46f710e058c9c1646273e6b225acf47cbed"}}